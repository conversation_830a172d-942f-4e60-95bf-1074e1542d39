// out: false
@import 'vw_values.less';
@import 'constants.less'; 
// import roboto
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

* {
  box-sizing: border-box; 
  cursor:default;
  letter-spacing: 0;
  margin:0; 
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &:focus {
    outline: none;
  }
}

 
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

html {
  overflow-x: hidden;
}
body {
  background: @hardWhite;
  color: @primaryColor;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-style: normal;
  overflow: hidden;
  line-height: 1;
  font-size: @vw20;
  .transitionMore(background, .6s);
  &:has(.artistPage), &:has(.bookingRequestBlock), &:has(.teamHeaderBlock) {
    color: @hardWhite;
    background: @primaryColor;
    section {
      &:last-of-type {
        padding-bottom: @vw100 + @vw40;
      }
    }
    .mainCursor {
      &.show {
        opacity: .2;
      }
    }
  }
  strong, em {
    font-weight: 700;
  }
  p {
    line-height: 1.5;
    font-weight: 500;
    a {
      cursor: pointer;
      color: @primaryColor;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
  ul {
    line-height: 1.4;
  }
}

img {
  pointer-events: none;
}

body.touch {
  a,
  .button {
    &:hover {
      opacity: 1 !important;
      color: inherit !important;
      background: inherit !important;
      transform: none !important;
      transition: none !important;
    }
  }
  .button {
    &:hover {
      background: @primaryColor !important;
    }
  }
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

[data-parallax] {
  will-change: transform;
}

section {
  margin: @vw100 + @vw40 0;
  &:first-of-type {
    margin-top: 0;
    padding-top: @vw100 * 2;
  }
   &:last-of-type {
    &.ctaBlock {
      margin-bottom: 0;
    }
  }
  &.noMarginBottom {
    margin-bottom: 0;
  }
  &.whiteBackground {
    .sliderButton, .arrowButton {
      background: transparent;
    }
  }
  &.primary {
    &:before {
      content: '';
      background: @primaryColor;
      position: absolute;
      width: 100%;
      z-index: -2;
      height: calc(100% ~"+" @vw100 + @vw40 + @vw100 + @vw40);
      top: -@vw100 - @vw40;
    }
    a {
      color: @hardWhite;
    }
  }
  &.white {
    padding: @vw100 0;
    border-radius: @vw30;
    color: @hardBlack;
    background: @hardWhite;
  }
  .staggerWordsChild(@i, @transition, @delay: 0s) when (@i > 0) {
    &:nth-child(@{i}) {
        .word {
          transition-delay: (@i * @transition + @delay);
        }
    }
    .staggerWordsChild(@i - 1, @transition, @delay);
  }
  &.inview {
    [data-lines] {
      visibility: visible;
      .line {
        .staggerWordsChild(100, 0.15s, 0.4s);
        .word {
          .transform(translateY(0));
          .transitionMore(transform, .75s, 0s, cubic-bezier(0, 0.55, 0.45, 1));
          .stagger(100, 0.05s);
        }
      }
    }
  }
  [data-lines] {
    .line {
      position: relative; 
      overflow: hidden;
      .word {
        .transform(translateY(100%));
        will-change: transform;
      }
    }
  }
}

[data-lines] {
  visibility: hidden;
}

#pageContainer {
  position: relative;
  overflow: hidden;
}

.contentWrapper {
  display: block;
  width: 100%;
  padding: 0 @vw100 + @vw40; 
  &.smaller {
    padding: 0 @vw100 + @vw40 + @vw106 + @vw16;
  }
}

.noise {
  background-color: rgba(20,20,20,.3);
  background-position: 0;
  height: 100%;
  opacity: .6;
  pointer-events: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
}

@keyframes noise{ 
  0%{
      background-position: 0 0;
  } 
  100%{
      background-position: 100% 100%;
  }
}

.button {
  .rounded(@vw6);
  position: relative;
  // overflow: hidden;
  display: inline-flex;
  cursor: pointer;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  height: @vw55;
  text-align: left;
  font-size: @vw18;
  color: @hardWhite;
  text-decoration: none;
  .transitionMore(color, .3s);
  &.primary {
    color: @primaryColor;
    &:after {
      border-color: @primaryColor;
    }
     .arrows {
        background: @primaryColor;
        color: @hardWhite;
      }
  }
  &:not(.primary) {
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255,255,255,.1);
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(6px);
    }
  }
  &:after {
    content: '';
    z-index: 0;
    .rounded(@vw6);
    pointer-events: none;
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid @hardWhite;
    .transform(translate(-50%,-50%));
    height: calc(100% ~"-" 2px);
    width: calc(100% ~"-" 2px);
  }
  * {
    cursor: pointer;
  }
  &:hover {
    .innerText {
      padding-left: @vw36;
      padding-right: @vw26;
    }
    .arrows {
      color: @hardWhite;
      &:before {
        .transform(translate(-50%,-50%) scale(1.06));
      }
      i {
        &:first-child {
          .transform(translate(-50%, -50%));
          opacity: 1;
        }
        &:last-child {
          .transform(translate(100%, -200%) scale(.5));
          opacity: 0;
        }
      }
    }
  }
  .innerText {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - @vw55);
    padding-left: @vw26;
    line-height: @vw55;
    padding-right: @vw36;
    transition: padding-left .3s, padding-right .3s;
    -webkit-transition: padding-left .3s, padding-right .3s;
    overflow: hidden;
    display: -webkit-inline-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .arrows {
    display: inline-block;
    vertical-align: middle;
    background: @hardWhite;
    width: @vw55;
    height: @vw55;
    font-size: @vw20;
    .rounded(@vw6);
    line-height: @vw34;
    text-align: center;
    z-index: 2;
    right: -2px;
    position: relative;
    color: @secondaryColor;
    overflow: hidden;
    // .transform(scale(1.1));
    .transitionMore(border-color,.3s);
    &:before {
      content: '';
      background: @secondaryColor;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      .rounded(@vw6);
      .transform(translate(-50%,-50%) scale(0));
      .transitionMore(transform, .45s, 0s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      .transform(translate(-50%, -50%));
      transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
      -webkit-transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
      &:first-child {
        .transform(translate(-200%, 100%) scale(.5));
        opacity: 0;
      }
    }
  }
}

.textLink {
  display: inline-table;
  cursor: pointer;
  font-size: @vw18;
  line-height: 1.4;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: @secondaryColorLight;
  text-transform: uppercase;
  text-decoration: none;
  .transitionMore(opacity,.3s);
  &.bigger {
    font-size: @vw50;
    .innerText {
      display: inline;
      padding-right: @vw30;
      width: calc(100% - @vw64);
    }
    .arrows {
      width: @vw50;
      height: @vw50;
      font-size: @vw30;
      line-height: @vw50;
    }
  }
  * {
    cursor: pointer;
  }
  &:hover {
    opacity: .4;
    .arrows {
      i {
        &:first-child {
          .transform(translate(-50%, -50%));
          opacity: 1;
        }
        &:last-child {
          .transform(translate(50%, -150%) scale(.5));
          opacity: 0;
        }
      }
    }
  }
  .innerText {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - @vw20);
    padding-right: @vw16;
  }
  .arrows {
    display: inline-block;
    vertical-align: middle;
    width: @vw20;
    height: @vw20;
    line-height: @vw34;
    text-align: center;
    position: relative;
    overflow: hidden;
    .transitionMore(border-color,.3s);
    i {
      position: absolute;
      font-size: @vw16;
      left: 50%;
      top: 50%;
      .transform(translate(-50%, -50%));
      transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
      -webkit-transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
      &:first-child {
        .transform(translate(-150%, 50%) scale(.5));
        opacity: 0;
      }
    }
  }
}

.arrowButton {
  cursor: pointer;
  display: inline-block;
  border: 1px solid @hardWhite;
  color: @hardWhite;
  .rounded(50%);
  text-align: center;
  line-height: @vw52;
  width: @vw55;
  height: @vw55;
  overflow: hidden;
  position: relative;
  vertical-align: middle;
  transition: color .3s, background-color .3s;
  &.primary {
    color: @primaryColor;
    border-color: @primaryColor;
  }
  &.prev {
    &:hover {
      i {
        &:last-child {
          .transform(translate(-300%, -50%) scale(.5));
          opacity: 0;
        }
      }
    }
    i {
      &:first-child {
        .transform(translate(300%, -50%) scale(.5));
        opacity: 0;
      }
    }
  }
  &.disabled {
    opacity: .3;
    pointer-events: none;
  }
  &:hover {
    background-color: @hardWhite;
    color: @primaryColor;
    i {
      &:first-child {
        .transform(translate(-50%, -50%));
        opacity: 1;
      }
      &:last-child {
        .transform(translate(300%, -50%) scale(.5));
        opacity: 0;
      }
    }
  }
  i {
    cursor: pointer;
    position: absolute;
    left: 50%;
    font-size: @vw20;
    top: 50%;
    .transform(translate(-50%, -50%));
    transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
    -webkit-transition: opacity .6s 0s ease-in-out, transform .6s 0s cubic-bezier(0.85, 0, 0.15, 1);
    &:first-child {
      .transform(translate(-300%, -50%) scale(.5));
      opacity: 0;
    }
  }
}

#background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.dg.ac {
  z-index: 3 !important;
}

@media all and (max-width: 1080px) {
  body {
    font-size: @vw18-1080;
  }
  section {
    margin: @vw100-1080 + @vw20-1080 0;
    &:first-of-type {
      padding-top: @vw100-1080 * 2;
    }
    &.white {
      padding: @vw100-1080 0;
      border-radius: @vw30-1080;
    }
    &.primary {
      &:before {
        height: calc(100% ~"+" @vw100-1080 + @vw40-1080 + @vw100-1080 + @vw40-1080);
        top: -@vw100-1080 - @vw40-1080;
      }
    }
  }
  .contentWrapper {
    padding: 0 @vw40-1080;
    &.smaller {
      padding: 0 @vw40-1080 + @vw32-1080;
    }
  }
  .button {
    .rounded(@vw6-1080);
    height: @vw55-1080;
    font-size: @vw18-1080;
    &:after {
      .rounded(@vw6-1080);
    }
    &:hover {
      .innerText {
        padding-left: @vw36-1080;
        padding-right: @vw26-1080;
      }
    }
    .innerText {
      width: calc(100% - @vw55-1080);
      padding-left: @vw26-1080;
      line-height: @vw55-1080;
      padding-right: @vw36-1080;
    }
    .arrows {
      width: @vw55-1080;
      height: @vw55-1080;
      font-size: @vw20-1080;
      .rounded(@vw6-1080);
      line-height: @vw34-1080;
      &:before {
        .rounded(@vw6-1080);
      }
    }
  }
  .textLink {
    font-size: @vw14-1080;
    .innerText {
      width: calc(100% - @vw34-1080);
      padding-right: @vw16-1080;
    }
    &.bigger {
      font-size: @vw50-1080;
      .innerText {
        padding-right: @vw30-1080;
        width: calc(100% - @vw64-1080);
      }
      .arrows {
        width: @vw50-1080;
        height: @vw50-1080;
        font-size: @vw30-1080;
        line-height: @vw50-1080;
        i {
          font-size: @vw30-1080;
        }
      }
    }
    .arrows {
      width: @vw34-1080;
      height: @vw34-1080;
      line-height: @vw34-1080;
    }
  }
  .arrowButton {
    line-height: @vw52-1080;
    width: @vw55-1080;
    height: @vw55-1080;
    i {
      font-size: @vw20-1080;
    }
  }
}

@media all and (max-width: 580px) {
  body {
    font-size: @vw24-580;
    line-height: 1.2;
  }
  section {
    margin: @vw100-580 + @vw20-580 0;
    &:first-of-type {
      padding-top: @vw100-580 * 2.5;
    }
    &.white {
      padding: @vw100-580 0;
      border-radius: @vw30-580;
    }
    &.primary {
      &:before {
        height: calc(100% ~"+" @vw100-580 + @vw40-580 + @vw100-580 + @vw40-580);
        top: -@vw100-580 - @vw40-580;
      }
    }
  }
  .contentWrapper {
    padding: 0 @vw22-580;
    &.smaller {
      padding: 0 @vw22-580;
    }
  }
  .button {
    .rounded(@vw6-580);
    height: @vw70-580;
    font-size: @vw22-580;
    &:after {
      .rounded(@vw6-580);
    }
    &:hover {
      .innerText {
        padding-left: @vw36-580;
        padding-right: @vw26-580;
      }
    }
    .innerText {
      width: calc(100% - @vw55-580);
      padding-left: @vw26-580;
      line-height: @vw70-580;
      padding-right: @vw36-580;
    }
    .arrows {
      width: @vw70-580;
      height: @vw70-580;
      font-size: @vw24-580;
      .rounded(@vw6-580);
      line-height: @vw70-580;
      &:before {
        .rounded(@vw6-580);
      }
    }
  }
  .textLink {
    font-size: @vw24-580;
    &.bigger {
      font-size: @vw24-580;
      .innerText {
        width: calc(100% - @vw34-580);
        padding-right: @vw16-580;
      }
      .arrows {
        width: @vw34-580;
        height: @vw34-580;
        line-height: @vw34-580;
      }
    }
    .innerText {
      width: calc(100% - @vw34-580);
      padding-right: @vw16-580;
    }
    .arrows {
      width: @vw34-580;
      height: @vw34-580;
      line-height: @vw34-580;
      i {
        font-size: @vw16-580;
      }
    }
  }
  .arrowButton {
    line-height: @vw70-580;
    width: @vw70-580;
    height: @vw70-580;
    i {
      font-size: @vw20-580;
    }
  }
}
