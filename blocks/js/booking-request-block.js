$(document).ready(function(){
    $(document).on("initPage", function () {
        if ($(".bookingRequestBlock").length > 0) {
            initializeBookingRequestBlock();
        }
    });
});

function initializeBookingRequestBlock() {
    const $block = $('.bookingRequestBlock');
    const $steps = $block.find('.formStep');
    const $stepIndicators = $block.find('.stepIndicator');
    const $progressFill = $block.find('.progressFill');
    const $prevButton = $block.find('.prevButton');
    const $nextButton = $block.find('.nextButton');
    const $submitButton = $block.find('.submitButton');

    let currentStep = 1;
    let totalSteps = 6; // Exact 6 stappen
    const STORAGE_KEY = 'booking_request_form_data';
    const STORAGE_EXPIRY_DAYS = 7; // Cache expires after 7 days

    // Load saved form data from localStorage
    loadFormDataFromStorage();

    // Initialize
    updateProgressBar();
    updateNavigationButtons();

    // Check for pre-selected artist from URL parameter
    preselectArtistFromURL();

    // Initialize Select2 for all select fields
    // initializeSelect2();

    // Hide infoIcons without matching infoText and initialize info functionality
    initializeInfoIcons();

    // Navigation event handlers (with namespaced events for Swup compatibility)
    $(document).off('click.bookingNextButton').on('click.bookingNextButton', '.bookingRequestBlock .nextButton', function() {
        if (validateCurrentStep()) {
            nextStep();
            scrollToBlock();
        } else {
            showValidationError('Please complete all required fields before proceeding to the next step.');
        }
    });

    $(document).off('click.bookingPrevButton').on('click.bookingPrevButton', '.bookingRequestBlock .prevButton', function() {
        prevStep();
        scrollToBlock();
    });

    $(document).off('click.bookingSubmitButton').on('click.bookingSubmitButton', '.bookingRequestBlock .submitButton', function() {
        if (validateAllSteps()) {
            submitForm();
        } else {
            showValidationError('Please complete all required fields before submitting.');
        }
    });

    // Step indicator click handlers - alleen toegestaan naar vorige stappen of sequentieel naar volgende
    $(document).off('click.bookingStepIndicator').on('click.bookingStepIndicator', '.bookingRequestBlock .stepIndicator', function() {
        const targetStep = parseInt($(this).data('step'));

        // Alleen toestaan als:
        // 1. Terug naar een vorige stap (targetStep < currentStep)
        // 2. Of exact de volgende stap (targetStep === currentStep + 1) en huidige stap is geldig
        if (targetStep < currentStep) {
            goToStep(targetStep);
            scrollToBlock();
        } else if (targetStep === currentStep + 1) {
            // Alleen naar volgende stap als huidige stap geldig is
            if (validateCurrentStep()) {
                goToStep(targetStep);
                scrollToBlock();
            } else {
                showValidationError('Please complete the current step before proceeding to the next step.');
            }
        } else if (targetStep > currentStep + 1) {
            // Niet toegestaan om stappen over te slaan
            showValidationError('Please complete all steps in order. You cannot skip steps.');
        }
    });

    // Artist selection handlers (with namespaced events for Swup compatibility)
    $(document).off('change.bookingArtistSelection').on('change.bookingArtistSelection', '.bookingRequestBlock .artistOption input[type="checkbox"]', function() {
        const $option = $(this).closest('.artistOption');
        if ($(this).is(':checked')) {
            $option.addClass('selected');
        } else {
            $option.removeClass('selected');
        }
        updateSelectedArtistsCount();
        saveFormDataToStorage();
    });

    // Form field change handlers for real-time validation and storage (with namespaced events for Swup compatibility)
    $(document).off('change.bookingFormFields blur.bookingFormFields').on('change.bookingFormFields blur.bookingFormFields', '.bookingRequestBlock input, .bookingRequestBlock select, .bookingRequestBlock textarea', function() {
        validateField($(this));
        saveFormDataToStorage();
    });

    // Scroll to block top using Lenis
    function scrollToBlock() {
        if (typeof scroller !== 'undefined' && scroller) {
            scroller.scrollTo($block[0], {
                offset: -100, // Offset for header
                duration: 1.2,
                easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
            });
        }
    }

    // Save form data to localStorage
    function saveFormDataToStorage() {
        const formData = {
            currentStep: currentStep,
            timestamp: Date.now(),
            data: {}
        };

        // Save all form field values
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            if (name) {
                if ($field.is(':checkbox')) {
                    formData.data[name] = $field.is(':checked');
                } else if ($field.is(':radio')) {
                    if ($field.is(':checked')) {
                        formData.data[name] = $field.val();
                    }
                } else {
                    formData.data[name] = $field.val();
                }
            }
        });

        // Save selected artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).val());
        });
        formData.data.selectedArtists = selectedArtists;

        localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
    }

    // Load form data from localStorage
    function loadFormDataFromStorage() {
        try {
            const savedData = localStorage.getItem(STORAGE_KEY);
            if (!savedData) return;

            const formData = JSON.parse(savedData);

            // Check if data is expired
            const daysSinceStored = (Date.now() - formData.timestamp) / (1000 * 60 * 60 * 24);
            if (daysSinceStored > STORAGE_EXPIRY_DAYS) {
                localStorage.removeItem(STORAGE_KEY);
                return;
            }

            // Restore form field values
            Object.entries(formData.data).forEach(([name, value]) => {
                if (name === 'selectedArtists') {
                    // Handle selected artists
                    value.forEach(artistId => {
                        const $checkbox = $block.find(`.artistOption input[type="checkbox"][value="${artistId}"]`);
                        if ($checkbox.length) {
                            $checkbox.prop('checked', true);
                            $checkbox.closest('.artistOption').addClass('selected');
                        }
                    });
                    updateSelectedArtistsCount();
                } else {
                    const $field = $block.find(`[name="${name}"]`);
                    if ($field.length) {
                        if ($field.is(':checkbox')) {
                            $field.prop('checked', value);
                        } else if ($field.is(':radio')) {
                            $field.filter(`[value="${value}"]`).prop('checked', true);
                        } else {
                            $field.val(value);
                            // Trigger Select2 update if it's a select field
                            if ($field.is('select') && $field.hasClass('select2-hidden-accessible')) {
                                $field.trigger('change.select2');
                            }
                        }
                    }
                }
            });

            // Restore current step
            if (formData.currentStep && formData.currentStep > 1 && formData.currentStep <= totalSteps) {
                currentStep = formData.currentStep;
                updateStep();
            }
        } catch (error) {
            console.error('Error loading form data from storage:', error);
            localStorage.removeItem(STORAGE_KEY);
        }
    }

    // Clear form data from localStorage
    function clearFormDataFromStorage() {
        localStorage.removeItem(STORAGE_KEY);
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStep();
            saveFormDataToStorage();
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStep();
            saveFormDataToStorage();
        }
    }

    function goToStep(step) {
        if (step >= 1 && step <= totalSteps) {
            currentStep = step;
            updateStep();
            saveFormDataToStorage();
        }
    }
    
    function updateStep() {
        $steps.removeClass('active');
        $stepIndicators.removeClass('active completed disabled');

        // Activeer de huidige stap
        $steps.filter(`[data-step="${currentStep}"]`).addClass('active');
        $stepIndicators.filter(`[data-step="${currentStep}"]`).addClass('active');

        // Markeer voorgaande stappen als voltooid
        for (let i = 1; i < currentStep; i++) {
            $stepIndicators.filter(`[data-step="${i}"]`).addClass('completed');
        }

        // Markeer stappen die meer dan 1 stap vooruit zijn als disabled
        for (let i = currentStep + 2; i <= totalSteps; i++) {
            $stepIndicators.filter(`[data-step="${i}"]`).addClass('disabled');
        }

        // Update de review summary als we op stap 6 zijn (laatste stap)
        if (currentStep === 6) {
            updateReviewSummary();
        }

        updateProgressBar();
        updateNavigationButtons();
        animateStepTransition();
    }
    
    function updateProgressBar() {
        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
        gsap.to($progressFill, {
            width: `${progress}%`,
            duration: 0.5,
            ease: 'power2.out'
        });
    }

    function updateNavigationButtons() {
        // Verberg alle submit buttons eerst
        $submitButton.hide();
        $block.find('.submitButton').hide();

        if (currentStep === totalSteps) {
            // Last step (step 6): show "submit request" button, hide "next" button
            $nextButton.hide();
            $submitButton.show();
            $submitButton.text('Submit Request');
        } else {
            // All other steps: show "next" button
            $nextButton.show();
            $nextButton.text('Next');
        }

        // Previous button: verberg op stap 1, toon op alle andere stappen
        if (currentStep === 1) {
            $prevButton.hide();
        } else {
            $prevButton.show();
        }
    }
    
    function animateStepTransition() {
        const $activeStep = $steps.filter('.active');
        if (!$activeStep.length) return; // Fix: only animate if step exists
        // Animate step content in
        const $title = $activeStep.find('.stepTitle, .stepDescription');
        const $content = $activeStep.find('.formFields, .artistSelection, .reviewSummary');
        if ($title.length) {
            gsap.fromTo($title, {
                y: 30,
                opacity: 0
            }, {
                y: 0,
                opacity: 1,
                duration: 0.6,
                stagger: 0.1,
                ease: 'power2.out'
            });
        }
        if ($content.length) {
            gsap.fromTo($content, {
                y: 40,
                opacity: 0
            }, {
                y: 0,
                opacity: 1,
                duration: 0.8,
                delay: 0.2,
                ease: 'power2.out'
            });
        }
    }
    
    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                return validateArtistSelection();
            case 2:
                return validateEventDetails();
            case 3:
                return validateStepFields(3);
            case 4:
                return validateStepFields(4);
            case 5:
                return validateStepFields(5);
            case 6:
                return validateAllSteps(); // Laatste stap: valideer alles
            default:
                return true;
        }
    }

    function validateStepFields(stepNumber) {
        const $currentStepElement = $steps.filter(`[data-step="${stepNumber}"]`);
        let isValid = true;
        const missingFields = [];

        // Vind alle velden in deze stap (niet alleen required)
        const $allFields = $currentStepElement.find('input, select, textarea').filter(':visible');

        $allFields.each(function() {
            const $field = $(this);
            const fieldName = $field.attr('name');
            const isRequired = $field.prop('required') || $field.hasClass('required');
            let value = '';

            if ($field.is('select')) {
                value = $field.find('option:selected').val();
                // Voor select velden, check of er een geldige optie is geselecteerd
                if (value === '' || value === null || value === undefined) {
                    value = '';
                }
            } else if ($field.is(':checkbox')) {
                value = $field.is(':checked') ? $field.val() : '';
            } else if ($field.is(':radio')) {
                // Voor radio buttons, check of er een optie is geselecteerd in de groep
                const radioName = $field.attr('name');
                const $checkedRadio = $currentStepElement.find(`input[name="${radioName}"]:checked`);
                value = $checkedRadio.length ? $checkedRadio.val() : '';
            } else {
                value = typeof $field.val() === 'string' ? $field.val().trim() : '';
            }

            // Valideer alleen als het veld required is
            if (isRequired && !value) {
                markFieldAsInvalid($field);
                isValid = false;
                missingFields.push(fieldName || 'unnamed field');
            } else if (value) {
                // Extra validatie voor specifieke veld types
                if ($field.attr('type') === 'email' || fieldName === 'contact_email') {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        markFieldAsInvalid($field);
                        isValid = false;
                        missingFields.push(fieldName + ' (invalid email format)');
                    } else {
                        markFieldAsValid($field);
                    }
                } else if ($field.attr('type') === 'tel' || fieldName === 'contact_phone') {
                    // Basic phone validation
                    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                    if (!phoneRegex.test(value)) {
                        markFieldAsInvalid($field);
                        isValid = false;
                        missingFields.push(fieldName + ' (invalid phone format)');
                    } else {
                        markFieldAsValid($field);
                    }
                } else {
                    markFieldAsValid($field);
                }
            } else if (!isRequired) {
                // Veld is niet required en leeg, dat is oké
                $field.removeClass('invalid valid');
            }
        });

        if (!isValid) {
            console.log(`Step ${stepNumber} validation failed. Missing fields:`, missingFields);
        }

        return isValid;
    }

    function validateAllSteps() {
        for (let i = 1; i <= totalSteps; i++) {
            if (i === 1) {
                if (!validateArtistSelection()) return false;
            } else if (i === 2) {
                if (!validateEventDetails()) return false;
            } else {
                if (!validateStepFields(i)) return false;
            }
        }
        return true;
    }


    
    function validateArtistSelection() {
        const selectedArtists = $block.find('.artistOption input[type="checkbox"]:checked');
        if (selectedArtists.length === 0) {
            return false;
        }
        return true;
    }

    function validateEventDetails() {
        return validateStepFields(2);
    }
    

    
    function validateField($field) {
        const value = $field.val().trim();
        const isRequired = $field.prop('required');
        
        if (isRequired && !value) {
            markFieldAsInvalid($field);
            return false;
        } else if (value) {
            markFieldAsValid($field);
            return true;
        }
        
        return true;
    }
    
    function markFieldAsInvalid($field) {
        $field.addClass('invalid').removeClass('valid');
        $field.closest('.fieldGroup').addClass('has-error');
    }
    
    function markFieldAsValid($field) {
        $field.addClass('valid').removeClass('invalid');
        $field.closest('.fieldGroup').removeClass('has-error');
    }
    
    function showValidationError(message) {
        // Remove existing error messages
        $block.find('.validation-error').remove();
        
        // Add new error message
        const $error = $('<div class="validation-error">' + message + '</div>');
        $block.find('.formNavigation').before($error);
        
        // Animate error message
        gsap.fromTo($error, {
            opacity: 0,
            y: -20
        }, {
            opacity: 1,
            y: 0,
            duration: 0.3
        });
        
        // Remove error after 5 seconds
        setTimeout(() => {
            gsap.to($error, {
                opacity: 0,
                y: -20,
                duration: 0.3,
                onComplete: () => $error.remove()
            });
        }, 5000);
    }
    
    function updateSelectedArtistsCount() {
        const count = $block.find('.artistOption input[type="checkbox"]:checked').length;
        const $counter = $block.find('.selected-artists-counter');

        if ($counter.length === 0 && count > 0) {
            const $counter = $('<div class="selected-artists-counter">' + count + ' artist(s) selected</div>');
            $block.find('.artistSelection').after($counter);
        } else if ($counter.length > 0) {
            if (count > 0) {
                $counter.text(count + ' artist(s) selected');
            } else {
                $counter.remove();
            }
        }
    }

    function preselectArtistFromURL() {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const artistId = urlParams.get('artist_id');

        if (artistId) {
            // Find and select the artist checkbox
            const $artistCheckbox = $block.find(`.artistOption input[type="checkbox"][value="${artistId}"]`);

            if ($artistCheckbox.length > 0) {
                // Check the checkbox
                $artistCheckbox.prop('checked', true);

                // Add selected class to the option
                $artistCheckbox.closest('.artistOption').addClass('selected');

                // Update the counter
                updateSelectedArtistsCount();

                // Save to localStorage
                saveFormDataToStorage();

                // Scroll to the selected artist using Lenis if available
                const $selectedOption = $artistCheckbox.closest('.artistOption');
                if ($selectedOption.length > 0) {
                    setTimeout(() => {
                        if (typeof scroller !== 'undefined' && scroller) {
                            scroller.scrollTo($selectedOption[0], {
                                offset: -150,
                                duration: 1.2,
                                easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
                            });
                        } else {
                            $selectedOption[0].scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    }, 500);
                }
            }
        }
    }
    
    function updateReviewSummary() {
        // 1. Selected Artists
        const $selectedArtists = $block.find('.artistOption input[type="checkbox"]:checked');
        const $artistsList = $block.find('.selectedArtistsList');
        $artistsList.empty();
        if ($selectedArtists.length) {
            $selectedArtists.each(function() {
                const $option = $(this).closest('.artistOption');
                const artistName = $(this).data('artist-name') || $option.find('label').text().trim();
                $artistsList.append($('<div class="review-row"></div>').text(artistName));
            });
        } else {
            $artistsList.append('<div class="review-row">None selected</div>');
        }

        // 2. Event Details (Step 2)
        updateSummarySection('.eventDetailsSummary', 2);

        // 3. Contact Information - Clear first, then add steps 3, 4, and 5
        const $contactSummary = $block.find('.contactInfoSummary');
        $contactSummary.empty();
        updateSummarySection('.contactInfoSummary', 3);
        updateSummarySection('.contactInfoSummary', 4);
        updateSummarySection('.contactInfoSummary', 5);

        // 4. Additional Information (any remaining fields or specific additional info)
        const $additionalSummary = $block.find('.additionalInfoSummary');
        $additionalSummary.empty();

        // Check for any additional fields that might not be in the main steps
        $block.find('textarea[name*="additional"], input[name*="additional"], textarea[name*="comment"], input[name*="comment"], textarea[name*="note"], input[name*="note"]').each(function() {
            const $field = $(this);
            const value = $field.val();
            if (value && value.trim()) {
                const label = $field.closest('.fieldGroup').find('label').text().replace('*', '').trim() || 'Additional Information';
                const $row = $('<div class="review-row"></div>');
                $row.append($('<strong></strong>').text(label + ': '));
                $row.append($('<span></span>').text(value));
                $additionalSummary.append($row);
            }
        });

        // If no additional info found, show message
        if ($additionalSummary.children().length === 0) {
            $additionalSummary.append('<div class="review-row"><em>No additional information provided</em></div>');
        }
    }

    function updateSummarySection(summarySelector, stepNumber) {
        const $summary = $block.find(summarySelector);

        // Don't empty if we're adding to an existing section (like contactInfoSummary)
        if (stepNumber === 3 || stepNumber === 4) {
            // For steps 3 and 4, we're adding to contactInfoSummary, don't empty it
        } else {
            $summary.empty();
        }

        $block.find(`.formStep[data-step="${stepNumber}"] .fieldGroup`).each(function() {
            const $group = $(this);
            const $label = $group.find('label').first();
            const $field = $group.find('input, select, textarea').first();

            if ($field.length && $label.length) {
                let labelText = $label.text().replace('*', '').trim();
                let value = '';

                if ($field.is('select')) {
                    const selectedOption = $field.find('option:selected');
                    value = selectedOption.text() || selectedOption.val() || 'Not provided';
                } else if ($field.is(':checkbox')) {
                    value = $field.is(':checked') ? 'Yes' : 'No';
                } else if ($field.is(':radio')) {
                    const checkedRadio = $group.find('input[type="radio"]:checked');
                    value = checkedRadio.length ? checkedRadio.val() : 'Not provided';
                } else {
                    value = $field.val() || 'Not provided';
                }

                // Alleen tonen als er een waarde is of als het een verplicht veld is
                if (value && value !== 'Not provided') {
                    const $row = $('<div class="review-row"></div>');
                    $row.append($('<strong></strong>').text(labelText + ': '));
                    $row.append($('<span></span>').text(value));
                    $summary.append($row);
                }
            }
        });

        // Als er geen gegevens zijn en dit is de eerste keer dat we deze sectie vullen, toon een bericht
        if ($summary.children().length === 0 && stepNumber !== 3 && stepNumber !== 4) {
            $summary.append('<div class="review-row"><em>No information provided</em></div>');
        }
    }

    function collectFormData() {
        const data = {};

        // Selected artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).data('artist-name') || $(this).val());
        });
        data.selected_artists = selectedArtists.join(', ');

        // All form fields
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');

            if (name && !name.includes('selected_artists')) {
                if ($field.is('select')) {
                    data[name] = $field.find('option:selected').val() || '';
                } else if ($field.is(':checkbox')) {
                    data[name] = $field.is(':checked') ? $field.val() : '';
                } else if ($field.is(':radio')) {
                    if ($field.is(':checked')) {
                        data[name] = $field.val();
                    }
                } else {
                    data[name] = $field.val() || '';
                }
            }
        });

        return data;
    }

    function populateContactForm7(data) {
        const $cf7Form = $block.find('.hiddenForm form');
        if ($cf7Form.length === 0) return;

        // Remove all previously added hidden fields (except the default _wpcf7 fields)
        $cf7Form.find('input[type="hidden"]').each(function() {
            const name = $(this).attr('name');
            if (name && !name.startsWith('_wpcf7')) {
                $(this).remove();
            }
        });

        // Create hidden fields for all data
        Object.entries(data).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                const $hiddenField = $('<input type="hidden" name="' + key + '" value="' + String(value).replace(/"/g, '&quot;') + '">');
                $cf7Form.append($hiddenField);
            }
        });

        console.log('ContactForm7 data prepared:', data);
    }

    function submitForm() {
        console.log('Submitting booking request form...');

        // Valideer eerst alle stappen
        if (!validateAllSteps()) {
            showValidationError('Please complete all required fields before submitting.');
            return;
        }

        // Verzamel alle form data
        const formData = collectFormData();
        console.log('Collected form data:', formData);

        // Populate hidden Contact Form 7 fields
        populateContactForm7(formData);

        // Disable submit button en toon loading state
        $submitButton.prop('disabled', true).addClass('loading').text('Sending...');

        // Submit het formulier
        const $cf7Form = $block.find('.hiddenForm form');
        if ($cf7Form.length) {
            console.log('Submitting Contact Form 7 form...');
            $cf7Form.submit();
        } else {
            console.error('Contact Form 7 form not found');
            showValidationError('A technical problem occurred. Please try again later.');
            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        }
    }



    function resetBookingForm() {
        // Reset all form fields
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);

            if ($field.is(':checkbox') || $field.is(':radio')) {
                $field.prop('checked', false);
            } else if ($field.is('select')) {
                $field.prop('selectedIndex', 0);
                // Trigger Select2 update if it's a select field
                if ($field.hasClass('select2-hidden-accessible')) {
                    $field.trigger('change.select2');
                }
            } else {
                $field.val('');
            }

            // Remove validation classes
            $field.removeClass('valid invalid');
        });

        // Reset artist selections
        $block.find('.artistOption').removeClass('selected');
        $block.find('.selected-artists-counter').remove();

        // Reset to step 1
        currentStep = 1;
        updateStep();

        // Clear localStorage
        clearFormDataFromStorage();

        console.log('Booking form has been reset');
    }

    // Initialize Select2 for all select fields
    function initializeSelect2() {
        $block.find('select').each(function() {
            const $select = $(this);

            // Only initialize if not already initialized
            if (!$select.hasClass('select2-hidden-accessible')) {
                $select.select2({
                    width: '100%',
                    placeholder: $select.attr('placeholder') || 'Select an option...',
                    allowClear: true,
                    minimumResultsForSearch: 10 // Hide search box if less than 10 options
                });
            }
        });
    }

    // Initialize info icons functionality
    function initializeInfoIcons() {
        // Hide infoIcons without matching infoText in .information
        $block.find('.infoIcon').each(function() {
            const infoKey = $(this).data('info');
            const $infoPanel = $block.find('.information');
            const $infoText = $infoPanel.find('.infoText[data-info="' + infoKey + '"]');

            if ($infoText.length === 0) {
                $(this).remove(); // Remove instead of hide for cleaner DOM
            }
        });
    }



    // Contact Form 7 event handlers
    $(document).off('wpcf7mailsent.bookingRequest').on('wpcf7mailsent.bookingRequest', function(event) {
        // Check if this event is from our booking request form
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('Contact Form 7 mail sent successfully - preparing redirect');

            // Reset the entire form
            resetBookingForm();

            // Clear form data from localStorage
            clearFormDataFromStorage();

            // Add a small delay to ensure everything is processed
            setTimeout(function() {
                console.log('Redirecting to thank you page...');
                window.location.href = '/booking-request/thank-you/';
            }, 500);
        }
    });

    $(document).off('wpcf7invalid.bookingRequest').on('wpcf7invalid.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            showValidationError('Not all fields are filled in correctly. Please check your information and try again.');
            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        }
    });

    $(document).off('wpcf7mailfailed.bookingRequest').on('wpcf7mailfailed.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            showValidationError('Something went wrong with sending. Please try again later.');
            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        }
    });

    // InfoIcon explanation functionality
    $(document).off("click.bookingInfoIcon").on("click.bookingInfoIcon", ".bookingRequestBlock .infoIcon", function(e) {
        e.preventDefault();
        const infoKey = $(this).data("info");
        const $infoText = $block.find('.infoText[data-info="' + infoKey + '"]');

        // Hide all other info texts first
        $block.find('.infoText').fadeOut(200);

        // Show the selected info text
        setTimeout(function() {
            $infoText.fadeIn(200);
        }, 200);

        // Scroll to top of block
        scrollToBlock();
    });

    // Hide info text when clicking outside
    $(document).off("click.bookingInfoHide").on("click.bookingInfoHide", function(e) {
        if (!$(e.target).closest('.infoIcon, .infoText').length) {
            $block.find('.infoText').fadeOut(200);
        }
    });
}
