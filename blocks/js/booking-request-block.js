$(document).ready(function(){
    $(document).on("initPage", function () {
        if ($(".bookingRequestBlock").length > 0) {
            initializeBookingRequestBlock();
        }
    });
});

function initializeBookingRequestBlock() {
    const $block = $('.bookingRequestBlock');
    const $steps = $block.find('.formStep');
    const $stepIndicators = $block.find('.stepIndicator');
    const $progressFill = $block.find('.progressFill');
    const $prevButton = $block.find('.prevButton');
    const $nextButton = $block.find('.nextButton');
    const $submitButton = $block.find('.submitButton');

    let currentStep = 1;
    let totalSteps = 6; // Exact 6 stappen
    const STORAGE_KEY = 'booking_request_form_data';
    const STORAGE_EXPIRY_DAYS = 7; // Cache expires after 7 days

    // Load saved form data from localStorage
    loadFormDataFromStorage();

    // Initialize
    updateProgressBar();
    updateNavigationButtons();

    // Ensure summary is generated if we're on step 6 (after page load/refresh)
    if (currentStep === 6) {
        console.log('📋 Page loaded on step 6 - ensuring summary is generated...');
        setTimeout(function() {
            updateReviewSummary();
        }, 100); // Small delay to ensure all data is loaded
    }

    // Check for pre-selected artist from URL parameter
    preselectArtistFromURL();

    // Initialize Select2 for all select fields
    // initializeSelect2();

    // Hide infoIcons without matching infoText and initialize info functionality
    initializeInfoIcons();

    // Navigation event handlers (with namespaced events for Swup compatibility)
    $(document).off('click.bookingNextButton').on('click.bookingNextButton', '.bookingRequestBlock .nextButton', function() {
        if (validateCurrentStep()) {
            nextStep();
            scrollToBlock();
        } else {
            showValidationError('Please complete all required fields before proceeding to the next step.');
        }
    });

    $(document).off('click.bookingPrevButton').on('click.bookingPrevButton', '.bookingRequestBlock .prevButton', function() {
        prevStep();
        scrollToBlock();
    });

    $(document).off('click.bookingSubmitButton').on('click.bookingSubmitButton', '.bookingRequestBlock .submitButton', function() {
        if (validateAllSteps()) {
            submitForm();
        } else {
            showValidationError('Please complete all required fields before submitting.');
        }
    });

    // Step indicator click handlers - alleen toegestaan naar vorige stappen of sequentieel naar volgende
    $(document).off('click.bookingStepIndicator').on('click.bookingStepIndicator', '.bookingRequestBlock .stepIndicator', function() {
        const targetStep = parseInt($(this).data('step'));

        // Alleen toestaan als:
        // 1. Terug naar een vorige stap (targetStep < currentStep)
        // 2. Of exact de volgende stap (targetStep === currentStep + 1) en huidige stap is geldig
        if (targetStep < currentStep) {
            goToStep(targetStep);
            scrollToBlock();
        } else if (targetStep === currentStep + 1) {
            // Alleen naar volgende stap als huidige stap geldig is
            if (validateCurrentStep()) {
                goToStep(targetStep);
                scrollToBlock();
            } else {
                showValidationError('Please complete the current step before proceeding to the next step.');
            }
        } else if (targetStep > currentStep + 1) {
            // Niet toegestaan om stappen over te slaan
            showValidationError('Please complete all steps in order. You cannot skip steps.');
        }
    });

    // Artist selection handlers (with namespaced events for Swup compatibility)
    $(document).off('change.bookingArtistSelection').on('change.bookingArtistSelection', '.bookingRequestBlock .artistOption input[type="checkbox"]', function() {
        const $option = $(this).closest('.artistOption');
        if ($(this).is(':checked')) {
            $option.addClass('selected');
        } else {
            $option.removeClass('selected');
        }
        updateSelectedArtistsCount();
        saveFormDataToStorage();
    });

    // Form field change handlers for real-time validation and storage (with namespaced events for Swup compatibility)
    $(document).off('change.bookingFormFields blur.bookingFormFields').on('change.bookingFormFields blur.bookingFormFields', '.bookingRequestBlock input, .bookingRequestBlock select, .bookingRequestBlock textarea', function() {
        validateField($(this));
        saveFormDataToStorage();
    });

    // Scroll to block top using Lenis
    function scrollToBlock() {
        if (typeof scroller !== 'undefined' && scroller) {
            scroller.scrollTo($block[0], {
                offset: -100, // Offset for header
                duration: 1.2,
                easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
            });
        }
    }

    // Save form data to localStorage
    function saveFormDataToStorage() {
        const formData = {
            currentStep: currentStep,
            timestamp: Date.now(),
            data: {}
        };

        // Save all form field values
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            const value = $field.val();

            if (name) {
                if ($field.is(':checkbox')) {
                    const isChecked = $field.is(':checked');
                    formData.data[name] = isChecked;
                    if (isChecked) {
                        console.log(`💾 Saving checkbox ${name}: ${isChecked}`);
                    }
                } else if ($field.is(':radio')) {
                    if ($field.is(':checked')) {
                        formData.data[name] = $field.val();
                        console.log(`💾 Saving radio ${name}: ${$field.val()}`);
                    }
                } else if (value && value.trim() !== '') {
                    // Only save non-empty values for input/select/textarea
                    formData.data[name] = value;
                    console.log(`💾 Saving field ${name}: "${value}"`);
                }
            }
        });

        // Save selected artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).val());
        });
        if (selectedArtists.length > 0) {
            formData.data.selectedArtists = selectedArtists;
            console.log(`💾 Saving selected artists: ${selectedArtists.join(', ')}`);
        }

        localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
        console.log(`💾 Form data saved to localStorage (step ${currentStep}):`, Object.keys(formData.data).length, 'fields');
    }

    // Load form data from localStorage
    function loadFormDataFromStorage() {
        try {
            const savedData = localStorage.getItem(STORAGE_KEY);
            if (!savedData) return;

            const formData = JSON.parse(savedData);

            // Check if data is expired
            const daysSinceStored = (Date.now() - formData.timestamp) / (1000 * 60 * 60 * 24);
            if (daysSinceStored > STORAGE_EXPIRY_DAYS) {
                localStorage.removeItem(STORAGE_KEY);
                return;
            }

            // Restore form field values - be more selective about what we consider placeholders
            Object.entries(formData.data).forEach(([name, value]) => {
                // Skip if value is empty or null
                if (!value || value === '' || value === null || value === undefined) {
                    return;
                }

                // Only skip if value EXACTLY matches known placeholder patterns
                const isPlaceholder = typeof value === 'string' && (
                    value === 'Event Name' ||
                    value === 'Venue Name' ||
                    value === 'Country' ||
                    value === 'City' ||
                    value === 'Fee Offer (optional)' ||
                    value === 'Doors Open (Time, optional)' ||
                    value === 'Doors Close (Time, optional)' ||
                    value === 'Performance Time (optional)' ||
                    value === 'Event Line-up' ||
                    value === 'Estimated Ticket Price' ||
                    value === 'Billing Proposal' ||
                    value === 'Videos of previous events' ||
                    value === 'Event Website (optional)' ||
                    value === 'Company Address' ||
                    value === 'VAT Number (optional)' ||
                    value === 'Contact Person Email' ||
                    value === 'Contact Person Phone' ||
                    value === 'Nearest Airport (optional)' ||
                    value === 'Distance Airport-Venue (optional)'
                );

                if (isPlaceholder) {
                    console.log(`🗑️ Skipping exact placeholder match for ${name}: "${value}"`);
                    return;
                }

                if (name === 'selectedArtists') {
                    // Handle selected artists
                    value.forEach(artistId => {
                        const $checkbox = $block.find(`.artistOption input[type="checkbox"][value="${artistId}"]`);
                        if ($checkbox.length) {
                            $checkbox.prop('checked', true);
                            $checkbox.closest('.artistOption').addClass('selected');
                        }
                    });
                    updateSelectedArtistsCount();
                } else {
                    const $field = $block.find(`[name="${name}"]`);
                    if ($field.length) {
                        if ($field.is(':checkbox')) {
                            $field.prop('checked', value);
                        } else if ($field.is(':radio')) {
                            $field.filter(`[value="${value}"]`).prop('checked', true);
                        } else {
                            $field.val(value);
                            console.log(`✅ Restored valid value for ${name}: "${value}"`);
                            // Trigger Select2 update if it's a select field
                            if ($field.is('select') && $field.hasClass('select2-hidden-accessible')) {
                                $field.trigger('change.select2');
                            }
                        }
                    }
                }
            });

            // Restore current step
            if (formData.currentStep && formData.currentStep > 1 && formData.currentStep <= totalSteps) {
                currentStep = formData.currentStep;
                updateStep();

                // If we're on step 6 (summary), generate the summary with restored data
                if (currentStep === 6) {
                    console.log('📋 On step 6 after restore - generating summary with localStorage data...');
                    updateReviewSummary();
                }
            }
        } catch (error) {
            console.error('Error loading form data from storage:', error);
            localStorage.removeItem(STORAGE_KEY);
        }
    }

    // Clear form data from localStorage
    function clearFormDataFromStorage() {
        localStorage.removeItem(STORAGE_KEY);
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStep();
            saveFormDataToStorage();
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStep();
            saveFormDataToStorage();
        }
    }

    function goToStep(step) {
        if (step >= 1 && step <= totalSteps) {
            currentStep = step;
            updateStep();
            saveFormDataToStorage();
        }
    }
    
    function updateStep() {
        $steps.removeClass('active');
        $stepIndicators.removeClass('active completed disabled');

        // Activeer de huidige stap
        $steps.filter(`[data-step="${currentStep}"]`).addClass('active');
        $stepIndicators.filter(`[data-step="${currentStep}"]`).addClass('active');

        // Markeer voorgaande stappen als voltooid
        for (let i = 1; i < currentStep; i++) {
            $stepIndicators.filter(`[data-step="${i}"]`).addClass('completed');
        }

        // Markeer stappen die meer dan 1 stap vooruit zijn als disabled
        for (let i = currentStep + 2; i <= totalSteps; i++) {
            $stepIndicators.filter(`[data-step="${i}"]`).addClass('disabled');
        }

        // Update de review summary als we op stap 6 zijn (laatste stap)
        if (currentStep === 6) {
            updateReviewSummary();
        }

        updateProgressBar();
        updateNavigationButtons();
        animateStepTransition();
    }
    
    function updateProgressBar() {
        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
        gsap.to($progressFill, {
            width: `${progress}%`,
            duration: 0.5,
            ease: 'power2.out'
        });
    }

    function updateNavigationButtons() {
        // Verberg alle submit buttons eerst
        $submitButton.hide();
        $block.find('.submitButton').hide();

        if (currentStep === totalSteps) {
            // Last step (step 6): show "submit request" button, hide "next" button
            $nextButton.hide();
            $submitButton.show();
            $submitButton.text('Submit Request');
        } else {
            // All other steps: show "next" button
            $nextButton.show();
            $nextButton.html('<i class="icon-chevron-right"></i><i class="icon-chevron-right"></i>');
        }

        // Previous button: verberg op stap 1, toon op alle andere stappen
        if (currentStep === 1) {
            $prevButton.hide();
        } else {
            $prevButton.show();
        }
    }
    
    function animateStepTransition() {
        const $activeStep = $steps.filter('.active');
        if (!$activeStep.length) return; // Fix: only animate if step exists
        // Animate step content in
        const $title = $activeStep.find('.stepTitle, .stepDescription');
        const $content = $activeStep.find('.formFields, .artistSelection, .reviewSummary');
        if ($title.length) {
            gsap.fromTo($title, {
                y: 30,
                opacity: 0
            }, {
                y: 0,
                opacity: 1,
                duration: 0.6,
                stagger: 0.1,
                ease: 'power2.out'
            });
        }
        if ($content.length) {
            gsap.fromTo($content, {
                y: 40,
                opacity: 0
            }, {
                y: 0,
                opacity: 1,
                duration: 0.8,
                delay: 0.2,
                ease: 'power2.out'
            });
        }
    }
    
    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                return validateArtistSelection();
            case 2:
                return validateEventDetails();
            case 3:
                return validateStepFields(3);
            case 4:
                return validateStepFields(4);
            case 5:
                return validateStepFields(5);
            case 6:
                return validateAllSteps(); // Laatste stap: valideer alles
            default:
                return true;
        }
    }

    function validateStepFields(stepNumber) {
        const $currentStepElement = $steps.filter(`[data-step="${stepNumber}"]`);
        let isValid = true;
        const missingFields = [];

        // Vind alle velden in deze stap (niet alleen required)
        const $allFields = $currentStepElement.find('input, select, textarea').filter(':visible');

        $allFields.each(function() {
            const $field = $(this);
            const fieldName = $field.attr('name');
            const isRequired = $field.prop('required') || $field.hasClass('required');
            let value = '';

            if ($field.is('select')) {
                value = $field.find('option:selected').val();
                // Voor select velden, check of er een geldige optie is geselecteerd
                if (value === '' || value === null || value === undefined) {
                    value = '';
                }
            } else if ($field.is(':checkbox')) {
                value = $field.is(':checked') ? $field.val() : '';
            } else if ($field.is(':radio')) {
                // Voor radio buttons, check of er een optie is geselecteerd in de groep
                const radioName = $field.attr('name');
                const $checkedRadio = $currentStepElement.find(`input[name="${radioName}"]:checked`);
                value = $checkedRadio.length ? $checkedRadio.val() : '';
            } else {
                value = typeof $field.val() === 'string' ? $field.val().trim() : '';
            }

            // Valideer alleen als het veld required is
            if (isRequired && !value) {
                markFieldAsInvalid($field);
                isValid = false;
                missingFields.push(fieldName || 'unnamed field');
            } else if (value) {
                // Extra validatie voor specifieke veld types
                if ($field.attr('type') === 'email' || fieldName === 'contact_email') {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        markFieldAsInvalid($field);
                        isValid = false;
                        missingFields.push(fieldName + ' (invalid email format)');
                    } else {
                        markFieldAsValid($field);
                    }
                } else if ($field.attr('type') === 'tel' || fieldName === 'contact_phone') {
                    // Basic phone validation
                    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                    if (!phoneRegex.test(value)) {
                        markFieldAsInvalid($field);
                        isValid = false;
                        missingFields.push(fieldName + ' (invalid phone format)');
                    } else {
                        markFieldAsValid($field);
                    }
                } else {
                    markFieldAsValid($field);
                }
            } else if (!isRequired) {
                // Veld is niet required en leeg, dat is oké
                $field.removeClass('invalid valid');
            }
        });

        if (!isValid) {
            console.log(`Step ${stepNumber} validation failed. Missing fields:`, missingFields);
        }

        return isValid;
    }

    function validateAllSteps() {
        for (let i = 1; i <= totalSteps; i++) {
            if (i === 1) {
                if (!validateArtistSelection()) return false;
            } else if (i === 2) {
                if (!validateEventDetails()) return false;
            } else {
                if (!validateStepFields(i)) return false;
            }
        }
        return true;
    }


    
    function validateArtistSelection() {
        const selectedArtists = $block.find('.artistOption input[type="checkbox"]:checked');
        if (selectedArtists.length === 0) {
            return false;
        }
        return true;
    }

    function validateEventDetails() {
        return validateStepFields(2);
    }
    

    
    function validateField($field) {
        const value = $field.val().trim();
        const isRequired = $field.prop('required');
        
        if (isRequired && !value) {
            markFieldAsInvalid($field);
            return false;
        } else if (value) {
            markFieldAsValid($field);
            return true;
        }
        
        return true;
    }
    
    function markFieldAsInvalid($field) {
        $field.addClass('invalid').removeClass('valid');
        $field.closest('.fieldGroup').addClass('has-error');
    }
    
    function markFieldAsValid($field) {
        $field.addClass('valid').removeClass('invalid');
        $field.closest('.fieldGroup').removeClass('has-error');
    }
    
    function showValidationError(message) {
        // Remove existing error messages
        $block.find('.validation-error').remove();
        
        // Add new error message
        const $error = $('<div class="validation-error">' + message + '</div>');
        $block.find('.formNavigation').before($error);
        
        // Animate error message
        gsap.fromTo($error, {
            opacity: 0,
            y: -20
        }, {
            opacity: 1,
            y: 0,
            duration: 0.3
        });
        
        // Remove error after 5 seconds
        setTimeout(() => {
            gsap.to($error, {
                opacity: 0,
                y: -20,
                duration: 0.3,
                onComplete: () => $error.remove()
            });
        }, 5000);
    }
    
    function updateSelectedArtistsCount() {
        const count = $block.find('.artistOption input[type="checkbox"]:checked').length;
        const $counter = $block.find('.selected-artists-counter');

        if ($counter.length === 0 && count > 0) {
            const $counter = $('<div class="selected-artists-counter">' + count + ' artist(s) selected</div>');
            $block.find('.artistSelection').after($counter);
        } else if ($counter.length > 0) {
            if (count > 0) {
                $counter.text(count + ' artist(s) selected');
            } else {
                $counter.remove();
            }
        }
    }

    function preselectArtistFromURL() {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const artistId = urlParams.get('artist_id');

        if (artistId) {
            // Find and select the artist checkbox
            const $artistCheckbox = $block.find(`.artistOption input[type="checkbox"][value="${artistId}"]`);

            if ($artistCheckbox.length > 0) {
                // Check the checkbox
                $artistCheckbox.prop('checked', true);

                // Add selected class to the option
                $artistCheckbox.closest('.artistOption').addClass('selected');

                // Update the counter
                updateSelectedArtistsCount();

                // Save to localStorage
                saveFormDataToStorage();

                // Scroll to the selected artist using Lenis if available
                const $selectedOption = $artistCheckbox.closest('.artistOption');
                if ($selectedOption.length > 0) {
                    setTimeout(() => {
                        if (typeof scroller !== 'undefined' && scroller) {
                            scroller.scrollTo($selectedOption[0], {
                                offset: -150,
                                duration: 1.2,
                                easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
                            });
                        } else {
                            $selectedOption[0].scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    }, 500);
                }
            }
        }
    }
    
    function updateReviewSummary() {
        // 1. Selected Artists
        const $selectedArtists = $block.find('.artistOption input[type="checkbox"]:checked');
        const $artistsList = $block.find('.selectedArtistsList');
        $artistsList.empty();
        if ($selectedArtists.length) {
            $selectedArtists.each(function() {
                const $option = $(this).closest('.artistOption');
                const artistName = $(this).data('artist-name') || $option.find('label').text().trim();
                $artistsList.append($('<div class="review-row"></div>').text(artistName));
            });
        } else {
            $artistsList.append('<div class="review-row">None selected</div>');
        }

        // 2. Event Details (Step 2)
        updateSummarySection('.eventDetailsSummary', 2);

        // 3. Contact Information - Clear first, then add steps 3, 4, and 5
        const $contactSummary = $block.find('.contactInfoSummary');
        $contactSummary.empty();
        updateSummarySection('.contactInfoSummary', 3);
        updateSummarySection('.contactInfoSummary', 4);
        updateSummarySection('.contactInfoSummary', 5);

        // 4. Additional Information (any remaining fields or specific additional info)
        const $additionalSummary = $block.find('.additionalInfoSummary');
        $additionalSummary.empty();

        // Check for any additional fields that might not be in the main steps
        $block.find('textarea[name*="additional"], input[name*="additional"], textarea[name*="comment"], input[name*="comment"], textarea[name*="note"], input[name*="note"]').each(function() {
            const $field = $(this);
            const value = $field.val();
            if (value && value.trim()) {
                const label = $field.closest('.fieldGroup').find('label').text().replace('*', '').trim() || 'Additional Information';
                const $row = $('<div class="review-row"></div>');
                $row.append($('<strong></strong>').text(label + ': '));
                $row.append($('<span></span>').text(value));
                $additionalSummary.append($row);
            }
        });

        // If no additional info found, show message
        if ($additionalSummary.children().length === 0) {
            $additionalSummary.append('<div class="review-row"><em>No additional information provided</em></div>');
        }
    }

    function updateSummarySection(summarySelector, stepNumber) {
        const $summary = $block.find(summarySelector);

        // Don't empty if we're adding to an existing section (like contactInfoSummary)
        if (stepNumber === 3 || stepNumber === 4) {
            // For steps 3 and 4, we're adding to contactInfoSummary, don't empty it
        } else {
            $summary.empty();
        }

        $block.find(`.formStep[data-step="${stepNumber}"] .fieldGroup`).each(function() {
            const $group = $(this);
            const $label = $group.find('label').first();
            const $field = $group.find('input, select, textarea').first();

            if ($field.length && $label.length) {
                let labelText = $label.text().replace('*', '').trim();
                let value = '';

                if ($field.is('select')) {
                    const selectedOption = $field.find('option:selected');
                    value = selectedOption.text() || selectedOption.val() || 'Not provided';
                } else if ($field.is(':checkbox')) {
                    value = $field.is(':checked') ? 'Yes' : 'No';
                } else if ($field.is(':radio')) {
                    const checkedRadio = $group.find('input[type="radio"]:checked');
                    value = checkedRadio.length ? checkedRadio.val() : 'Not provided';
                } else {
                    value = $field.val() || 'Not provided';
                }

                // Alleen tonen als er een waarde is of als het een verplicht veld is
                if (value && value !== 'Not provided') {
                    const $row = $('<div class="review-row"></div>');
                    $row.append($('<strong></strong>').text(labelText + ': '));
                    $row.append($('<span></span>').text(value));
                    $summary.append($row);
                }
            }
        });

        // Als er geen gegevens zijn en dit is de eerste keer dat we deze sectie vullen, toon een bericht
        if ($summary.children().length === 0 && stepNumber !== 3 && stepNumber !== 4) {
            $summary.append('<div class="review-row"><em>No information provided</em></div>');
        }
    }

    function collectFormData() {
        const data = {};

        // Selected artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).data('artist-name') || $(this).val());
        });
        if (selectedArtists.length > 0) {
            data.selected_artists = selectedArtists.join(', ');
        }

        // All form fields - only collect actual user input, not placeholders
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            const value = $field.val();
            const placeholder = $field.attr('placeholder');

            if (name && !name.includes('selected_artists') && value) {
                // Skip if value matches placeholder or is a known placeholder pattern
                if (value === placeholder ||
                    value === 'Event Name' ||
                    value === 'Venue Name' ||
                    value === 'Country' ||
                    value === 'City' ||
                    value === 'Fee Offer (optional)' ||
                    value === 'Doors Open (Time, optional)' ||
                    value === 'Doors Close (Time, optional)' ||
                    value === 'Performance Time (optional)' ||
                    value === 'Event Line-up' ||
                    value === 'Estimated Ticket Price' ||
                    value === 'Billing Proposal' ||
                    value === 'Videos of previous events' ||
                    value === 'Event Website (optional)' ||
                    value === 'Company Address' ||
                    value === 'VAT Number (optional)' ||
                    value === 'Contact Person Email' ||
                    value === 'Contact Person Phone' ||
                    value === 'Nearest Airport (optional)' ||
                    value === 'Distance Airport-Venue (optional)' ||
                    value.includes('(optional)') ||
                    value.includes('(Time, optional)')) {

                    console.log(`🗑️ Skipping placeholder value for ${name}: "${value}"`);
                    return; // Skip this field
                }

                if ($field.is('select')) {
                    const selectedValue = $field.find('option:selected').val();
                    if (selectedValue && selectedValue !== '' && selectedValue !== '0') {
                        data[name] = selectedValue;
                        console.log(`✅ Collected select value for ${name}: "${selectedValue}"`);
                    }
                } else if ($field.is(':checkbox')) {
                    if ($field.is(':checked')) {
                        data[name] = $field.val();
                        console.log(`✅ Collected checkbox value for ${name}: "${$field.val()}"`);
                    }
                } else if ($field.is(':radio')) {
                    if ($field.is(':checked')) {
                        data[name] = $field.val();
                        console.log(`✅ Collected radio value for ${name}: "${$field.val()}"`);
                    }
                } else {
                    // Regular input/textarea - only if it has real content
                    data[name] = value;
                    console.log(`✅ Collected input value for ${name}: "${value}"`);
                }
            }
        });

        console.log('📊 Final collected data (no placeholders):', data);
        return data;
    }

    function populateContactForm7(data) {
        const $cf7Form = $block.find('.hiddenForm form');
        if ($cf7Form.length === 0) {
            console.error('CF7 form not found!');
            return;
        }

        console.log('🔧 Starting CF7 population with data:', data);

        // Remove all previously added hidden fields (except the default _wpcf7 fields)
        $cf7Form.find('input[type="hidden"]').each(function() {
            const name = $(this).attr('name');
            if (name && !name.startsWith('_wpcf7')) {
                $(this).remove();
            }
        });

        // Only use actual form data - no default values or placeholders
        const completeData = { ...data };

        // Clean up any placeholder text or empty values
        Object.keys(completeData).forEach(key => {
            const value = completeData[key];

            // Remove if it's a placeholder text, empty, or contains common placeholder patterns
            if (!value ||
                value === '' ||
                value === 'Not specified' ||
                value.includes('(optional)') ||
                value.includes('(Time, optional)') ||
                value.includes('Estimated') ||
                value.includes('Fee Offer') ||
                value.includes('Event Name') ||
                value.includes('Venue Name') ||
                value.includes('Country') ||
                value.includes('City') ||
                value.includes('Company Address') ||
                value.includes('VAT Number') ||
                value.includes('Contact Person') ||
                value.includes('Nearest Airport') ||
                value.includes('Distance Airport') ||
                value.includes('Event Website') ||
                value.includes('Videos of previous') ||
                value.includes('Event Line-up') ||
                value.includes('Billing Proposal')) {

                delete completeData[key];
                console.log(`🗑️ Removed placeholder/empty field: ${key} = "${value}"`);
            } else {
                console.log(`✅ Keeping valid field: ${key} = "${value}"`);
            }
        });

        // Create hidden fields for all data
        Object.entries(completeData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                const cleanValue = String(value).replace(/"/g, '&quot;');
                const $hiddenField = $('<input type="hidden" name="' + key + '" value="' + cleanValue + '">');
                $cf7Form.append($hiddenField);
                console.log(`📝 Added hidden field: ${key} = ${cleanValue}`);
            }
        });

        console.log('✅ ContactForm7 data preparation complete:', completeData);
    }

    function submitForm() {
        console.log('🚀 STARTING FORM SUBMISSION PROCESS');

        // Valideer eerst alle stappen
        if (!validateAllSteps()) {
            console.log('❌ Validation failed - stopping submission');
            showValidationError('Please complete all required fields before submitting.');
            return;
        }

        console.log('✅ All steps validated successfully');

        // Verzamel alle form data
        const formData = collectFormData();
        console.log('📊 Collected form data:', formData);

        // Populate hidden Contact Form 7 fields
        populateContactForm7(formData);

        // Disable submit button en toon loading state
        $submitButton.prop('disabled', true).addClass('loading').text('Sending...');
        console.log('🔄 Submit button disabled and set to loading state');

        // Submit het formulier
        const $cf7Form = $block.find('.hiddenForm form');
        if ($cf7Form.length) {
            console.log('📤 Found CF7 form, preparing submission...');

            // Force remove any existing CF7 validation classes that might interfere
            $cf7Form.removeClass('invalid submitting');
            $cf7Form.find('.wpcf7-not-valid').removeClass('wpcf7-not-valid');
            $cf7Form.find('.wpcf7-validation-errors').remove();
            console.log('🧹 Cleaned up existing CF7 validation classes');

            // Log all hidden fields that will be submitted
            console.log('📋 Hidden fields being submitted:');
            $cf7Form.find('input[type="hidden"]').each(function() {
                const name = $(this).attr('name');
                const value = $(this).val();
                if (!name.startsWith('_wpcf7')) {
                    console.log(`  - ${name}: ${value}`);
                }
            });

            // Submit using CF7's proper submission method
            console.log('🎯 SUBMITTING CF7 FORM (using CF7 submit method)...');

            // Add data-swup-ignore to prevent Swup from intercepting this form
            $cf7Form.attr('data-swup-ignore', 'true');

            // Log form details before submission
            console.log('📋 Form action:', $cf7Form.attr('action'));
            console.log('📋 Form method:', $cf7Form.attr('method'));
            console.log('📋 Form has data-swup-ignore:', $cf7Form.attr('data-swup-ignore'));

            // Wait for CF7 to be ready and then submit
            console.log('🚀 Waiting for CF7 to be ready...');

            // Check if CF7 is loaded
            if (typeof wpcf7 !== 'undefined') {
                console.log('✅ CF7 is loaded, proceeding with submission...');
                submitCF7Form($cf7Form);
            } else {
                console.log('⏳ CF7 not loaded yet, waiting...');
                // Wait for CF7 to load
                const waitForCF7 = setInterval(function() {
                    if (typeof wpcf7 !== 'undefined') {
                        console.log('✅ CF7 loaded after waiting, proceeding with submission...');
                        clearInterval(waitForCF7);
                        submitCF7Form($cf7Form);
                    }
                }, 100);

                // Timeout after 5 seconds
                setTimeout(function() {
                    clearInterval(waitForCF7);
                    console.log('⏰ CF7 loading timeout, trying fallback submission...');
                    submitCF7Form($cf7Form);
                }, 5000);
            }

            // Start monitoring for CF7 events
            startFormMonitoring();
        } else {
            console.error('❌ Contact Form 7 form not found');
            showValidationError('A technical problem occurred. Please try again later.');
            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        }
    }

    // Submit CF7 form using proper method
    function submitCF7Form($cf7Form) {
        console.log('🎯 EXECUTING CF7 FORM SUBMISSION...');

        // Try multiple submission methods
        const $cf7SubmitButton = $cf7Form.find('input[type="submit"], button[type="submit"]');

        if ($cf7SubmitButton.length) {
            console.log('✅ Found CF7 submit button, clicking it...');
            $cf7SubmitButton.trigger('click');
        } else {
            console.log('⚠️ No submit button found, trying form.submit()...');

            // Create a hidden submit button and click it
            const $hiddenSubmit = $('<input type="submit" style="position:absolute;left:-9999px;opacity:0;">');
            $cf7Form.append($hiddenSubmit);

            // Trigger the submit
            $hiddenSubmit.trigger('click');

            // Clean up
            setTimeout(function() {
                $hiddenSubmit.remove();
            }, 100);
        }

        console.log('🚀 CF7 form submission triggered');
    }

    // Handle successful form submission
    function handleSuccessfulSubmission() {
        console.log('🎉 HANDLING SUCCESSFUL SUBMISSION...');

        // Reset the form
        console.log('🔄 Resetting booking form after successful submission...');
        resetBookingForm();

        // Clear localStorage
        console.log('🗑️ Clearing localStorage after successful submission...');
        clearFormDataFromStorage();

        // Reset button
        $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        console.log('🔄 Submit button reset after successful submission');

        // Show success message
        console.log('🎉 Showing success message after successful submission...');
        showSuccessMessage();

        console.log('✅ SUCCESSFUL SUBMISSION HANDLED - redirect will happen in 2 seconds');
    }

    // Handle submission error
    function handleSubmissionError() {
        console.log('❌ HANDLING SUBMISSION ERROR...');

        $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        showValidationError('Something went wrong with sending. Please try again later.');
    }

    // Monitor CF7 form submission status
    function startFormMonitoring() {
        console.log('🔍 Starting CF7 form monitoring...');

        const $cf7Form = $block.find('.hiddenForm form');
        if (!$cf7Form.length) {
            console.error('❌ CF7 form not found for monitoring');
            return;
        }

        // Monitor for CF7 response with multiple checks
        let checkCount = 0;
        const maxChecks = 15; // Check for 15 seconds

        const checkSubmissionStatus = setInterval(function() {
            checkCount++;
            console.log(`🔍 Checking CF7 submission status (${checkCount}/${maxChecks})...`);

            // Check if CF7 has processed the form
            const $response = $cf7Form.find('.wpcf7-response-output');
            const hasResponse = $response.length > 0 && $response.text().trim() !== '';

            // Check form classes
            const isInvalid = $cf7Form.hasClass('invalid');
            const isSent = $cf7Form.hasClass('sent');
            const isSubmitting = $cf7Form.hasClass('submitting');
            const hasMailSent = $cf7Form.hasClass('mail-sent');

            console.log(`📊 CF7 Status: invalid=${isInvalid}, sent=${isSent}, submitting=${isSubmitting}, mailSent=${hasMailSent}, hasResponse=${hasResponse}`);

            if (hasResponse) {
                console.log(`📝 CF7 Response Output: "${$response.text()}"`);
            }

            // Check for Flamingo entry creation
            console.log('🔍 Checking if data reached Flamingo...');

            // If form is marked as sent or mail-sent, we're successful
            if (isSent || hasMailSent) {
                console.log('✅ CF7 CONFIRMED SUCCESSFUL SUBMISSION!');
                clearInterval(checkSubmissionStatus);
                handleFormSuccess();
                return;
            }

            // If form is marked as invalid, handle error
            if (isInvalid && !isSubmitting) {
                console.log('❌ CF7 MARKED FORM AS INVALID');
                clearInterval(checkSubmissionStatus);
                handleFormError('CF7 validation failed');
                return;
            }

            // If we've checked enough times without clear result
            if (checkCount >= maxChecks) {
                console.log('⏰ Max checks reached - checking final status...');
                clearInterval(checkSubmissionStatus);

                // Final check - if no clear error, assume success
                if (!isInvalid) {
                    console.log('✅ No errors detected - assuming successful submission');
                    handleFormSuccess();
                } else {
                    console.log('❌ Form appears to have errors - handling as error');
                    handleFormError('Timeout with validation errors');
                }
            }
        }, 1000); // Check every second
    }

    // Handle successful form submission
    function handleFormSuccess() {
        console.log('🎉 HANDLING SUCCESSFUL CF7 SUBMISSION...');

        // Reset the form
        console.log('🔄 Resetting booking form after successful submission...');
        resetBookingForm();

        // Clear localStorage
        console.log('🗑️ Clearing localStorage after successful submission...');
        clearFormDataFromStorage();

        // Reset button
        $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        console.log('🔄 Submit button reset after successful submission');

        // Show success message
        console.log('🎉 Showing success message after successful submission...');
        showSuccessMessage();

        console.log('✅ CF7 SUBMISSION HANDLED SUCCESSFULLY - Flamingo should have received the data');
    }

    // Handle form submission error
    function handleFormError(reason) {
        console.log(`❌ HANDLING CF7 SUBMISSION ERROR: ${reason}`);

        $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
        showValidationError('Please check all required fields and try again.');
    }



    function resetBookingForm() {
        console.log('🔄 Starting complete form reset...');

        // Reset all form fields
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const fieldName = $field.attr('name');

            if ($field.is(':checkbox') || $field.is(':radio')) {
                $field.prop('checked', false);
                console.log(`✅ Reset checkbox/radio: ${fieldName}`);
            } else if ($field.is('select')) {
                $field.prop('selectedIndex', 0);
                $field.val(''); // Ensure empty value
                // Trigger Select2 update if it's a select field
                if ($field.hasClass('select2-hidden-accessible')) {
                    $field.trigger('change.select2');
                }
                console.log(`✅ Reset select: ${fieldName}`);
            } else {
                $field.val(''); // Clear all text inputs, textareas, etc.
                console.log(`✅ Reset input: ${fieldName}`);
            }

            // Remove validation classes
            $field.removeClass('valid invalid');
        });

        // Reset artist selections
        $block.find('.artistOption').removeClass('selected');
        $block.find('.selected-artists-counter').remove();
        console.log('✅ Reset artist selections');

        // Reset to step 1
        currentStep = 1;
        updateStep();
        console.log('✅ Reset to step 1');

        // Clear localStorage
        clearFormDataFromStorage();
        console.log('✅ Cleared localStorage');

        // Remove any success messages
        $block.find('.booking-success-message').remove();

        // Show form elements again
        $block.find('.formStep').show();
        $block.find('.formNavigation').show();
        $block.find('.progressIndicator').show();
        console.log('✅ Restored form visibility');

        console.log('🎉 Booking form has been completely reset');
    }



    // Show success message
    function showSuccessMessage() {
        // Hide all form steps
        $block.find('.formStep').hide();
        $block.find('.formNavigation').hide();
        $block.find('.progressIndicator').hide();

        // Create and show success message
        const successHtml = `
            <div class="booking-success-message">
                <div class="success-icon">✓</div>
                <h3>Booking Request Submitted!</h3>
                <p>Thank you for your booking request. We have received your information and will get back to you soon.</p>
                <button class="new-request-button" onclick="location.reload()">Submit New Request</button>
            </div>
        `;

        $block.find('.formSteps').append(successHtml);

        // Try to trigger redirect after showing success message
        setTimeout(function() {
            console.log('🔄 Attempting manual redirect to thank you page...');
            window.location.href = '/booking-request/thank-you/';
        }, 2000);
    }



    // Initialize info icons functionality
    function initializeInfoIcons() {
        // Hide infoIcons without matching infoText in .information
        $block.find('.infoIcon').each(function() {
            const infoKey = $(this).data('info');
            const $infoPanel = $block.find('.information');
            const $infoText = $infoPanel.find('.infoText[data-info="' + infoKey + '"]');

            if ($infoText.length === 0) {
                $(this).remove(); // Remove instead of hide for cleaner DOM
            }
        });
    }



    // Contact Form 7 event handlers with Flamingo logging
    $(document).off('wpcf7mailsent.bookingRequest').on('wpcf7mailsent.bookingRequest', function(event) {
        // Check if this event is from our booking request form
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('🎉 CF7 MAIL SENT SUCCESSFULLY! Starting cleanup process...');
            console.log('🔥 FLAMINGO: Mail sent event triggered - data should now be in Flamingo plugin!');
            console.log('🔥 FLAMINGO: Check your WordPress admin > Flamingo > Inbound Messages');

            // Reset the entire form
            console.log('🔄 Resetting booking form after successful mail send...');
            resetBookingForm();

            // Clear form data from localStorage
            console.log('🗑️ Clearing localStorage after successful mail send...');
            clearFormDataFromStorage();

            // Reset button state
            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
            console.log('🔄 Submit button reset after successful mail send');

            console.log('✅ MAIL SENT SUCCESSFULLY - redirect plugin should handle redirect now');
            console.log('🔥 FLAMINGO: Form submission complete - check Flamingo admin for the entry!');
        }
    });

    // Handle CF7 submit response with Flamingo tracking
    $(document).off('wpcf7submit.bookingRequest').on('wpcf7submit.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('📨 CF7 SUBMIT EVENT TRIGGERED');
            console.log('🔥 FLAMINGO: Form submission started - checking if data will reach Flamingo...');
            console.log('Event detail:', event.originalEvent.detail);

            // Log the form data being submitted
            const $form = $(event.target);
            console.log('📋 FORM DATA BEING SUBMITTED TO CF7:');
            $form.find('input, select, textarea').each(function() {
                const $field = $(this);
                const name = $field.attr('name');
                const value = $field.val();
                if (name && value) {
                    console.log(`  ${name}: ${value}`);
                }
            });

            // Check if the form was actually sent (even if marked invalid)
            const detail = event.originalEvent.detail;
            if (detail && detail.apiResponse) {
                console.log('📋 CF7 API Response:', detail.apiResponse);
                console.log('🔥 FLAMINGO: CF7 API Response status:', detail.apiResponse.status);

                if (detail.apiResponse.status === 'mail_sent') {
                    console.log('✅ MAIL WAS SENT SUCCESSFULLY despite any validation status!');
                    console.log('🔥 FLAMINGO: Mail sent status confirmed - data should be in Flamingo now!');

                    // Reset the entire form
                    console.log('🔄 Resetting booking form after confirmed mail send...');
                    resetBookingForm();

                    // Clear form data from localStorage
                    console.log('🗑️ Clearing localStorage after confirmed mail send...');
                    clearFormDataFromStorage();

                    // Reset button state
                    $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
                    console.log('🔄 Submit button reset after confirmed mail send');

                    console.log('✅ CONFIRMED MAIL SENT - redirect plugin should handle redirect now');
                    console.log('🔥 FLAMINGO: Check WordPress admin > Flamingo > Inbound Messages for the entry!');
                } else {
                    console.log('❌ CF7 API Response indicates mail was NOT sent:', detail.apiResponse.status);
                    console.log('🔥 FLAMINGO: Mail not sent - data will NOT be in Flamingo');
                }
            } else {
                console.log('⚠️ No API response detail found in CF7 submit event');
                console.log('🔥 FLAMINGO: No API response - cannot determine if data reached Flamingo');
            }
        }
    });

    $(document).off('wpcf7invalid.bookingRequest').on('wpcf7invalid.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('❌ CF7 MARKED FORM AS INVALID');
            console.log('Event detail:', event.originalEvent.detail);

            // Log validation errors for debugging
            if (event.originalEvent.detail && event.originalEvent.detail.apiResponse) {
                console.log('🔍 CF7 Validation errors:', event.originalEvent.detail.apiResponse);
            }

            // Check if this is a false invalid (mail was actually sent)
            setTimeout(function() {
                // If we're still in loading state after 2 seconds, assume it failed
                if ($submitButton.hasClass('loading')) {
                    console.log('⏰ Form marked as invalid and no success detected after 2 seconds');
                    $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
                    showValidationError('Please check all required fields and try again. Some fields may be missing or incorrectly formatted.');
                }
            }, 2000);
        }
    });

    $(document).off('wpcf7mailfailed.bookingRequest').on('wpcf7mailfailed.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('❌ CF7 MAIL FAILED');
            console.log('Event detail:', event.originalEvent.detail);

            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
            showValidationError('Something went wrong with sending the email. Please try again later.');
        }
    });

    // Additional debugging event for CF7 spam detection
    $(document).off('wpcf7spam.bookingRequest').on('wpcf7spam.bookingRequest', function(event) {
        if ($(event.target).closest('.bookingRequestBlock').length > 0) {
            console.log('🚫 CF7 MARKED AS SPAM');
            console.log('Event detail:', event.originalEvent.detail);

            $submitButton.prop('disabled', false).removeClass('loading').text('Submit Request');
            showValidationError('Your submission was flagged as spam. Please try again or contact us directly.');
        }
    });

    // InfoIcon explanation functionality
    $(document).off("click.bookingInfoIcon").on("click.bookingInfoIcon", ".bookingRequestBlock .infoIcon", function(e) {
        e.preventDefault();
        const infoKey = $(this).data("info");
        const $infoText = $block.find('.infoText[data-info="' + infoKey + '"]');

        // Hide all other info texts first
        $block.find('.infoText').fadeOut(200);

        // Show the selected info text
        setTimeout(function() {
            $infoText.fadeIn(200);
        }, 200);

        // Scroll to top of block
        scrollToBlock();
    });

    // Hide info text when clicking outside
    $(document).off("click.bookingInfoHide").on("click.bookingInfoHide", function(e) {
        if (!$(e.target).closest('.infoIcon, .infoText').length) {
            $block.find('.infoText').fadeOut(200);
        }
    });
}
