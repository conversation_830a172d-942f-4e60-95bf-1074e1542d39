<?php
$title = get_field('title');
$subtitle = get_field('subtitle');
$text = get_field('text');
$contact_form_id = get_field('contact_form_id');

// Haal alle artiesten op voor de selectie
$artists = new WP_Query([
    'post_type' => 'artist',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'orderby' => 'title',
    'order' => 'ASC',
    'meta_query' => [
        [
            'key' => 'image',
            'compare' => 'EXISTS',
        ],
    ],
]);
?>

<section class="bookingRequestBlock" data-show-cursor data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <div class="col infoCol">
                <div class="formHeader">
                    <h1 class="tinyTitle white" data-lines data-words>Booking Request</h1>
                </div>
                <div class="progressIndicator">
                    <div class="progressBar">
                        <div class="progressFill"></div>
                    </div>
                </div>

                <!-- Info Panel for field explanations -->
                <div class="information">
                    <div class="infoText" data-info="fee_offer" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Fee Offer</strong><br>State your proposed artist fee. This is required for us to consider your request.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="room_count" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;How many rooms?</strong><br>Enter the number of rooms or stages at your event.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="artist_room_capacity" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Room Capacity</strong><br>Specify the capacity of the room where the artist will perform.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="soundsystem_spec" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Soundsystem Specification</strong><br>Describe the sound system in the room where the artist will play.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="event_lineup" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Event Line-up</strong><br>List all artists performing at the event.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="room_lineup" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Room Line-up</strong><br>Provide the running order for the specific room/stage where the artist will play.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="average_ticket_price" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Average Ticket Price</strong><br>State the average ticket price for your event.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="sponsor" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Sponsor</strong><br>List the main sponsor(s) of your event.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="soundsystem" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Soundsystem Specification</strong><br>Provide details about the sound system in the room.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="is_headlining" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Is the artist headlining?</strong><br>Indicate if the artist is the main act of the event.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="billing_proposal" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Billing Proposal</strong><br>If the artist is not headlining, describe their billing position.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="logo_use_possible" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Logo Use Possible?</strong><br>Let us know if the artist's logo can be used for event promotion.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="past_guests" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Past Guests</strong><br>List notable guests who have performed at your venue in the last 6 months.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="upcoming_guests" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Upcoming Guests</strong><br>List notable guests who are scheduled to perform in the next 6 months.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="previous_event_videos" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Videos of Previous Events</strong><br>Add links to videos of previous events for reference.</p>
                        </div>
                    </div>
                    <div class="infoText" data-info="airport_venue_distance" style="display:none;">
                        <div class="text">
                            <p><strong><i class='icon-info'></i>&nbsp;Distance Airport-Venue</strong><br>Indicate the distance and travel time from the nearest airport to the venue.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col formCol">
                <div class="formSteps">
                    <!-- Step 1: Requested Artists -->
                    <div class="formStep active" data-step="1">
                        <h3 class="tinyTitle">Requested Artists: *</h3>
                        <div class="artistSelection">
                            <?php if ($artists->have_posts()): ?>
                                <?php foreach ($artists->posts as $post):
                                    setup_postdata($post);
                                    $title = get_the_title($post->ID);
                                    $image = get_field('image', $post->ID);
                                    $roster_type = get_field('roster_type', $post->ID);
                                ?>
                                    <div class="artistOption" data-roster-type="<?php echo esc_attr($roster_type); ?>">
                                        <label class="artistLabel">
                                            <input type="checkbox" name="selected_artists[]" value="<?php echo esc_attr($post->ID); ?>" data-artist-name="<?php echo esc_attr($title); ?>">
                                            <div class="artistCard">
                                                <?php if ($image): ?>
                                                    <div class="artistImage">
                                                        <img src="<?php echo esc_url($image['sizes']['medium']); ?>" alt="<?php echo esc_attr($title); ?>">
                                                    </div>
                                                <?php endif; ?>
                                                <div class="artistInfo">
                                                    <h4 class="tinyTitle" class="artistName tinyTitle"><?php echo esc_html($title); ?></h4>
                                                    <span class="artistType"><?php echo $roster_type === 'ones_to_watch' ? 'Ones to Watch' : 'Main Roster'; ?></span>
                                                </div>
                                                <div class="checkmark"></div>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                                <?php wp_reset_postdata(); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Step 2: Event Details -->
                    <div class="formStep" data-step="2">
                        <div class="formFields">
                            <div class="fieldGroup full-width">
                                <label for="event_name">Event Name *</label>
                                <input type="text" id="event_name" name="event_name" placeholder="Name of your event" required>
                                <span class="infoIcon" data-info="event_name">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="event_date">Date of the Event *</label>
                                <input type="date" id="event_date" name="event_date" required>
                                <span class="infoIcon" data-info="event_date">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="venue_name">Venue Name *</label>
                                <input type="text" id="venue_name" name="venue_name" placeholder="Name of the venue" required>
                                <span class="infoIcon" data-info="venue_name">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup">
                                <label for="country">Country *</label>
                                <input type="text" id="country" name="country" placeholder="Country where event takes place" required>
                                <span class="infoIcon" data-info="country">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup">
                                <label for="city">City *</label>
                                <input type="text" id="city" name="city" placeholder="City where event takes place" required>
                                <span class="infoIcon" data-info="city">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="fee_offer">Fee Offer *</label>
                                <input type="text" id="fee_offer" name="fee_offer" placeholder="Your fee offer (e.g., €5,000)" required>
                                <span class="infoIcon" data-info="fee_offer">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="venue_capacity">Venue Total Capacity *</label>
                                <input type="number" id="venue_capacity" name="venue_capacity" placeholder="Total capacity of venue" required>
                                <span class="infoIcon" data-info="venue_capacity">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup three">
                                <label for="doors_open">Doors Open (Time) *</label>
                                <input type="time" id="doors_open" name="doors_open" required>
                                <span class="infoIcon" data-info="doors_open">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup three">
                                <label for="doors_close">Doors Close (Time) *</label>
                                <input type="time" id="doors_close" name="doors_close" required>
                                <span class="infoIcon" data-info="doors_close">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup three">
                                <label for="performance_time">Performance Time *</label>
                                <input type="time" id="performance_time" name="performance_time" required>
                                <span class="infoIcon" data-info="performance_time">
                                    <i class="icon-info"></i>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Venue & Technical -->
                    <div class="formStep" data-step="3">
                        <div class="formFields">
                            <div class="fieldGroup full-width">
                                <label for="room_count">How many rooms? *</label>
                                <input type="number" id="room_count" name="room_count" placeholder="Number of rooms/stages" required>
                                <span class="infoIcon" data-info="room_count">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="artist_room_capacity">Capacity of the room where the artist would play *</label>
                                <input type="number" id="artist_room_capacity" name="artist_room_capacity" placeholder="Room capacity" required>
                                <span class="infoIcon" data-info="artist_room_capacity">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="soundsystem_spec">Soundsystem specification in this room *</label>
                                <textarea id="soundsystem_spec" name="soundsystem_spec" rows="3" placeholder="Describe the sound system specifications..." required></textarea>
                                <span class="infoIcon" data-info="soundsystem_spec">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="event_lineup">Line-up of the event *</label>
                                <textarea id="event_lineup" name="event_lineup" rows="4" placeholder="List all artists performing at the event..." required></textarea>
                                <span class="infoIcon" data-info="event_lineup">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="room_lineup">Line-up / running order of the room where the artist would play *</label>
                                <textarea id="room_lineup" name="room_lineup" rows="4" placeholder="Running order for the specific room/stage..." required></textarea>
                                <span class="infoIcon" data-info="room_lineup">
                                    <i class="icon-info"></i>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Business Details -->
                    <div class="formStep" data-step="4">
                        <div class="formFields">
                            <div class="fieldGroup full-width">
                                <label for="average_ticket_price">Average Ticket Price *</label>
                                <input type="text" id="average_ticket_price" name="average_ticket_price" placeholder="e.g., €25" required>
                                <span class="infoIcon" data-info="average_ticket_price">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="sponsor">Sponsor *</label>
                                <input type="text" id="sponsor" name="sponsor" placeholder="Main sponsor(s)" required>
                                <span class="infoIcon" data-info="sponsor">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            
                            <div class="fieldGroup full-width">
                                <label for="soundsystem">Soundsystem specification in this room *</label>
                                <input type="text" id="soundsystem" name="soundsystem" placeholder="Soundsystem specification in this room *" required>
                                <span class="infoIcon" data-info="Soundsystem">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="is_headlining">Is the artist headlining the event? *</label>
                                <select id="is_headlining" name="is_headlining" required>
                                    <option value="">Select option</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                </select>
                                <span class="infoIcon" data-info="is_headlining">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="billing_proposal">If not headlining, what is your billing proposal? *</label>
                                <textarea id="billing_proposal" name="billing_proposal" rows="3" placeholder="Describe the billing position..."></textarea>
                                <span class="infoIcon" data-info="billing_proposal">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="logo_use_possible">Logo use possible? *</label>
                                <select id="logo_use_possible" name="logo_use_possible" required>
                                    <option value="">Select option</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                    <option value="with_approval">With approval</option>
                                </select>
                                <span class="infoIcon" data-info="logo_use_possible">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="past_guests">Past Guests in the last 6 months</label>
                                <textarea id="past_guests" name="past_guests" rows="3" placeholder="List notable past guests..."></textarea>
                                <span class="infoIcon" data-info="past_guests">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="upcoming_guests">Upcoming Guests in the next 6 months</label>
                                <textarea id="upcoming_guests" name="upcoming_guests" rows="3" placeholder="List upcoming guests..."></textarea>
                                <span class="infoIcon" data-info="upcoming_guests">
                                    <i class="icon-info"></i>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Event & Logistics Details -->
                    <div class="formStep" data-step="5">
                        <div class="formFields">
                            <div class="fieldGroup full-width">
                                <label for="previous_event_videos">Videos of previous events</label>
                                <textarea id="previous_event_videos" name="previous_event_videos" rows="3" placeholder="Add links to videos..."></textarea>
                                <span class="infoIcon" data-info="previous_event_videos">
                                    <i class="icon-info"></i>
                                </span>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="event_website">Website/Facebook of the event/club *</label>
                                <input type="text" id="event_website" name="event_website" placeholder="e.g., https://facebook.com/yourevent" required>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="company_address">Company name & Address *</label>
                                <input type="text" id="company_address" name="company_address" placeholder="Full company name and address" required>
                            </div>

                            <div class="fieldGroup full-width">
                                <label for="vat_number">VAT number *</label>
                                <input type="text" id="vat_number" name="vat_number" placeholder="e.g., NL123456789B01" required>
                            </div>

                            <div class="fieldGroup">
                                <label for="contact_email">Contact Person Email *</label>
                                <input type="email" id="contact_email" name="contact_email" placeholder="e.g., <EMAIL>" required>
                            </div>

                            <div class="fieldGroup">
                                <label for="contact_phone">Contact Person phone *</label>
                                <input type="tel" id="contact_phone" name="contact_phone" placeholder="e.g., +31 6 12345678" required>
                            </div>

                            <div class="fieldGroup">
                                <label for="nearest_airport">Nearest airport</label>
                                <input type="text" id="nearest_airport" name="nearest_airport" placeholder="e.g., Schiphol Airport">
                            </div>

                            <div class="fieldGroup">
                                <label for="airport_venue_distance">Distance Airport-Venue (km / h):</label>
                                <input type="text" id="airport_venue_distance" name="airport_venue_distance" placeholder="e.g., 25 km / 30 min">
                                <span class="infoIcon" data-info="airport_venue_distance">
                                    <i class="icon-info"></i>
                                </span>
                            </div>
                        </div>
                    </div>


                    <!-- Step 6: Review & Submit -->
                    <div class="formStep" data-step="6">
                        <div class="reviewSummary">
                            <div class="summarySection">
                                <h4 class="tinyTitle">Selected Artists</h4>
                                <div class="selectedArtistsList"></div>
                            </div>
                            
                            <div class="summarySection">
                                <h4 class="tinyTitle">Event Details</h4>
                                <div class="eventDetailsSummary"></div>
                            </div>
                            
                            <div class="summarySection">
                                <h4 class="tinyTitle">Contact Information</h4>
                                <div class="contactInfoSummary"></div>
                            </div>
                            
                            <div class="summarySection">
                                <h4 class="tinyTitle">Additional Information</h4>
                                <div class="additionalInfoSummary"></div>
                            </div>
                        </div>
                        
                        <div class="hiddenForm">
                            <?php if ($contact_form_id): ?>
                                <?php echo do_shortcode('[contact-form-7 id="' . $contact_form_id . '"]'); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

        <div class="progressIndicator">
            <div class="stepIndicators">
                 <button type="button" class="arrowButton navButton prevButton" disabled>
                    <i class="icon-chevron-left"></i><i class="icon-chevron-left"></i>
                </button>
                <div class="stepIndicator active" data-step="1">
                    <span class="stepNumber">1</span>
                    <!-- <span class="stepLabel">Artists</span> -->
                </div>
                <div class="stepIndicator" data-step="2">
                    <span class="stepNumber">2</span>
                    <!-- <span class="stepLabel">Event Details</span> -->
                </div>
                <div class="stepIndicator" data-step="3">
                    <span class="stepNumber">3</span>
                    <!-- <span class="stepLabel">Contact Info</span> -->
                </div>
                <div class="stepIndicator" data-step="4">
                    <span class="stepNumber">4</span>
                    <!-- <span class="stepLabel">Budget & Info</span> -->
                </div>
                <div class="stepIndicator" data-step="5">
                    <span class="stepNumber">5</span>
                    <!-- <span class="stepLabel">Review & Submit</span> -->
                </div>
                <div class="stepIndicator" data-step="6">
                    <span class="stepNumber">6</span>
                </div>
                <button type="button" class="arrowButton navButton nextButton">
                    <i class="icon-chevron-right"></i><i class="icon-chevron-right"></i>
                </button>
                <button type="button" class="arrowButton navButton submitButton submitButton-in-steps" style="display: none;">
                    <span class="buttonText">Verstuur aanvraag</span>
                </button>
            </div>
        </div>
            </div>
        </div>

        

    </div>
</section>
